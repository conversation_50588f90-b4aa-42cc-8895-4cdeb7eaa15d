import { NodeBackendBaseV1 } from "../../backend_types"

export default class FunctionNode extends NodeBackendBaseV1 {
    nodeType = "Function"
    private customFunction: Function | null = null

    setup() {
        console.log("Setting up FunctionNode:", this.context)

        // Listen for changes to the backend code
        this.ins.on("backendCode", (code: string) => {
            if (code) {
                try {
                    // Create a function from the provided code
                    // The function gets access to the node context
                    this.customFunction = new Function('context', 'ins', 'outs', "log", code)

                    // Execute the function with the node's context
                    this.executeCustomFunction()

                    // Log success
                    this.outs.set("_log", "Backend code compiled successfully")
                } catch (error) {
                    console.error("Error compiling backend code:", error)
                    this.outs.set("_log", `Error: ${error.message}`)
                    this.customFunction = null
                }
            }
        })

        // Listen for changes to input definitions
        this.ins.on("inputDefinitions", (definitions: object) => {
            console.log("Input definitions updated:", definitions)
            // When input definitions change, we need to set up listeners for each input
            if (definitions) {
                Object.keys(definitions).forEach(inputName => {
                    this.ins.on(inputName, (value: any) => {
                        // When an input value changes, re-execute the custom function
                        this.executeCustomFunction()
                    })
                })
            }
        })
    }

    // Log function to be called by backend code.
    log(...args: any[]) {
        console.log("FunctionNode:", ...args)
        this.outs.set("_log", args.join(" "))
    }

    executeCustomFunction() {
        if (!this.customFunction) return

        try {
            // Execute the custom function with the node's context
            this.customFunction(this.context, this.ins, this.outs, this.log.bind(this))
        } catch (error) {
            console.error("Error executing custom function:", error)
            this.outs.set("_log", `Runtime Error: ${error.message}`)
        }
    }

    cleanup() {
        console.log("Cleaning up FunctionNode:", this.context)
        this.customFunction = null
    }
}