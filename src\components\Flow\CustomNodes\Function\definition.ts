import { INodeDefinition } from "../../types"

export const nodeDefinition: INodeDefinition = {
    typeName: "Function",
    typeUID: "com.flow.function",
    category: "Basic",
    version: "1.0.0",
    author: "<PERSON>",
    description: "Function node, defined by backend script and front end VUE component script - both in typescript.",
    company: "JJ inc.",
    license: "MIT",
    ins: {
        backendCode: {
            valueType: "string",
            description: "Typescript for backend code. (backend.ts file)"
        },
        nodeUICode: {
            valueType: "string",
            description: "VUE component typescript file for node UI. (visual.ts file)"
        },
        inputDefinitions: {
            valueType: "object",
            description: "Inputs to the function node.",
            default: {
                "input1": {
                    valueType: "any",
                    description: "Input 1"
                }
            }
        },
        outputDefinitions: {
            valueType: "object",
            description: "Outputs from the function node.",
            default: {
                "output1": {
                    valueType: "any",
                    description: "Output 1"
                }
            }
        }
    },
    outs: {
        _log: {
            valueType: "string",
            description: "Log output from node backend."
        }
    }
}
