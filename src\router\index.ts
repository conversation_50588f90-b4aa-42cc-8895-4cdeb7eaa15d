import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/Home.vue'
import FlowView from '../views/Flow.vue'
import CoreDBBrowser from '../views/CoreDBBrowser.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/flow',
      name: 'flow',
      component: FlowView
    },
    {
      path: '/coredb',
      name: 'coredb',
      component: CoreDBBrowser
    }
  ]
})

export default router
